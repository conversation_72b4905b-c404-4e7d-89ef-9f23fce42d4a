[package]
name = "carbon-yellowstone-grpc-datasource"
description = "Yellowstone gRPC Datasource"
license = { workspace = true }
version = "0.9.0"
edition = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer", "yellowstone", "datasource"]
categories = ["encoding"]

[lib]
crate-type = ["rlib"]

[dependencies]
solana-account = { workspace = true }
solana-program = { workspace = true }
solana-pubkey = { workspace = true }
solana-signature = { workspace = true }

carbon-core = { workspace = true }

async-trait = { workspace = true }
futures = { workspace = true }
log = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true }
yellowstone-grpc-client = { workspace = true }
yellowstone-grpc-proto = { workspace = true }
