[package]
name = "carbon-cli"
version = "0.9.0"
edition = { workspace = true }
description = "CLI for Carbon"
license = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "cli"]
categories = ["command-line-utilities"]

[[bin]]
name = "carbon-cli"
path = "src/main.rs"

[dependencies]
solana-client = { workspace = true }
solana-commitment-config = { workspace = true }
solana-pubkey = { workspace = true }

anyhow = { workspace = true }
askama = { workspace = true }
borsh = { workspace = true, features = ["derive"] }
clap = { workspace = true, features = ["derive"] }
flate2 = { workspace = true }
heck = { workspace = true }
hex = { workspace = true }
inquire = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
sha2 = { workspace = true }
