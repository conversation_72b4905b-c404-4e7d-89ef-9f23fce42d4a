use {
    async_trait::async_trait,
    carbon_core::{
        deserialize::ArrangeAccounts,
        error::CarbonResult,
        instruction::{DecodedInstruction, InstructionMetadata, NestedInstruction},
        metrics::MetricsCollection,
        processor::Processor,
    },
    carbon_{{ metrics.module_name }}_metrics::{{ metrics.name }}Metrics,
    {%- for decoder in decoders %} 
    carbon_{{ decoder.module_name }}_decoder::{instructions::{{ decoder.name }}Instruction, {{ decoder.name }}Decoder, PROGRAM_ID as {{ decoder.name.to_uppercase() }}_PROGRAM_ID,}, 
    {%- endfor %} 
    {%- if data_source.module_name == "rpc_block_subscribe" %}
    carbon_rpc_block_subscribe_datasource::{Filters, RpcBlockSubscribe},
    solana_client::rpc_config::{RpcBlockSubscribeConfig, RpcBlockSubscribeFilter},
    {%- endif %}
    std::{env, sync::Arc},
    {%- if data_source.module_name == "yellowstone_grpc" %}
    std::collections::HashMap,
    carbon_yellowstone_grpc_datasource::YellowstoneGrpcGeyserClient,
    yellowstone_grpc_proto::geyser::{
        CommitmentLevel, SubscribeRequestFilterAccounts, SubscribeRequestFilterTransactions,
    },
    {%- endif %}
};

#[tokio::main]
pub async fn main() -> CarbonResult<()> {
    env_logger::init();
    dotenv::dotenv().ok();

    {%- if data_source.module_name == "rpc_block_subscribe" %}
    let datasource = RpcBlockSubscribe::new(rpc_ws_url, filters);

    let filters = Filters::new(
        RpcBlockSubscribeFilter::All,
        Some(RpcBlockSubscribeConfig {
            max_supported_transaction_version: Some(0),
            ..RpcBlockSubscribeConfig::default()
        }),
    );

    let rpc_ws_url =
        env::var("RPC_WS_URL").unwrap_or("wss://api.mainnet-beta.solana.com/".to_string());

    log::info!("Starting with RPC: {}", rpc_ws_url);
    {%- endif %}

    {%- if data_source.module_name == "helius_atlas_ws" %}
    let datasource = carbon_helius_atlas_ws_datasource::HeliusWebsocket::new(
        std::env::var("HELIUS_API_KEY").unwrap(),
        carbon_helius_atlas_ws_datasource::Filters {
            accounts: vec![],
            transactions: Some(RpcTransactionsConfig {
                filter: TransactionSubscribeFilter {
                    account_include: Some(vec![PUMPFUN_PROGRAM_ID.to_string().clone()]),
                    account_exclude: None,
                    account_required: None,
                    vote: None,
                    failed: None,
                    signature: None,
                },
                options: TransactionSubscribeOptions {
                    commitment: Some(TransactionCommitment::Confirmed),
                    encoding: Some(UiEnhancedTransactionEncoding::Base64),
                    transaction_details: Some(TransactionDetails::Full),
                    show_rewards: None,
                    max_supported_transaction_version: Some(0),
                },
            }),
        },
        Arc::new(RwLock::new(HashSet::new())),
        Cluster::MainnetBeta,
    );
    {%- endif %}

    {%- if data_source.module_name == "rpc_program_subscribe" %}
    
    {%- endif %}

    {%- if data_source.module_name == "rpc_transaction_crawler" %}
    let connection_config = ConnectionConfig::new(
        100,                                                                 // Batch limit
        Duration::from_secs(5),                                              // Polling interval
        5,                                                                   // Max Concurrent Requests
        RetryConfig::default(),                                              // Retry config
    );

    let datasource = RpcTransactionCrawler::new(
        env::var("RPC_URL").unwrap_or_default(),                             // RPC URL
        METEORA_PROGRAM_ID,                                                  // The test account
        connection_config,                                                    // Connection config
        filters,                                                             // Filters
        Some(solana_sdk::commitment_config::CommitmentConfig::finalized()),  // Commitment config
    );
    {%- endif %}

    {%- if data_source.module_name == "yellowstone_grpc" %}
    let mut account_filters: HashMap<String, SubscribeRequestFilterAccounts> = HashMap::new();
    account_filters.insert(
        "account_filter".to_string(),
        SubscribeRequestFilterAccounts {
            account: vec![],
            owner: vec![
                {%- for decoder in decoders %}
                {{ decoder.name.to_uppercase() }}_PROGRAM_ID.to_string().clone(),
                {%- endfor %} 
            ],
            filters: vec![],
            nonempty_txn_signature: None,
        },
    );

    let transaction_filter = SubscribeRequestFilterTransactions {
        vote: Some(false),
        failed: Some(false),
        account_include: vec![],
        account_exclude: vec![],
        account_required: vec![
            {%- for decoder in decoders %}
            {{ decoder.name.to_uppercase() }}_PROGRAM_ID.to_string().clone(),
            {%- endfor %} 
        ],
        signature: None,
    };

    let mut transaction_filters: HashMap<String, SubscribeRequestFilterTransactions> =
        HashMap::new();

    transaction_filters.insert("transaction_filter".to_string(), transaction_filter);

    let datasource = YellowstoneGrpcGeyserClient::new(
        env::var("GEYSER_URL").unwrap_or_default(),
        env::var("X_TOKEN").ok(),
        Some(CommitmentLevel::Confirmed),
        account_filters,
        transaction_filters,
        Arc::new(RwLock::new(HashSet::new())),
    );
    {%- endif %}

    carbon_core::pipeline::Pipeline::builder()
        .datasource(datasource)
        .metrics(Arc::new({{ metrics.name }}Metrics::new()))
        .metrics_flush_interval(5)
        {%- for decoder in decoders %}
        .instruction({{ decoder.name }}Decoder, {{ decoder.name }}InstructionProcessor)
        {%- endfor %} 
        .shutdown_strategy(carbon_core::pipeline::ShutdownStrategy::Immediate)
        .build()?
        .run()
        .await?;

    Ok(())
}

{%- for decoder in decoders %} 
pub struct {{ decoder.name }}InstructionProcessor;

#[async_trait]
impl Processor for {{ decoder.name }}InstructionProcessor {
    type InputType = (
        InstructionMetadata,
        DecodedInstruction<{{ decoder.name }}Instruction>,
        NestedInstructions,
    );

    async fn process(
        &mut self,
        (metadata, instruction, _nested_instructions): Self::InputType,
        _metrics: Arc<MetricsCollection>,
    ) -> CarbonResult<()> {
        let signature = metadata.transaction_metadata.signature;
        let accounts = instruction.accounts;

        match instruction.data {
            _ => {
                log::info!("received the {{ decoder.name }} instruction, sig: {}, accounts len: {}", signature, accounts.len());
            }
        };

        Ok(())
    }
}
{%- endfor %} 
