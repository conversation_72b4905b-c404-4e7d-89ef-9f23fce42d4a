[package]
name = "carbon-boop-decoder"
version = "0.9.0"
description = "Boop program decoder"
edition = { workspace = true }
license = { workspace = true }
readme = "README.md"
keywords = ["solana", "decoder", "boop"]
categories = ["encoding"]

[lib]
crate-type = ["rlib"]

[dependencies]
carbon-core = { workspace = true }
serde = { workspace = true }
solana-account = { workspace = true }
solana-instruction = { workspace = true, default-features = false }
solana-pubkey = { workspace = true }
