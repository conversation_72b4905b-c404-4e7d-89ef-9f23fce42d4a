use anyhow::{anyhow, Result};
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, SeedDerivable},
    signer::Signer,
    hash::Hash,
    instruction::Instruction,
    system_instruction,
    transaction::Transaction,
};
use solana_client::rpc_client::RpcClient;
use std::sync::Arc;
use tracing::{info, warn, error};

/// 账户类型枚举
#[derive(Debug, Clone)]
pub enum AccountType {
    Empty(EmptyAccountInfo),
    Nonce(NonceAccountInfo),
}

/// 空账户信息
#[derive(Debug, Clone)]
pub struct EmptyAccountInfo {
    pub address: Pubkey,
    pub lamports: u64,
    pub data_len: usize,
    pub owner: Pubkey,
}

/// Nonce账户信息结构
#[derive(Debug, Clone)]
pub struct NonceAccountInfo {
    pub address: Pubkey,
    pub nonce_value: Hash,
    pub is_initialized: bool,
    pub authority: Pubkey,
    pub fee_calculator_lamports_per_signature: u64,
    pub lamports: u64,
}

/// Nonce账户扫描器 - 专门用于查找现有的nonce账户
pub struct NonceScanner {
    rpc_client: Arc<RpcClient>,
    payer: Arc<Keypair>,
}

impl NonceScanner {
    /// 创建新的nonce扫描器
    pub fn new(rpc_url: &str, payer: Arc<Keypair>) -> Result<Self> {
        let rpc_client = Arc::new(RpcClient::new(rpc_url.to_string()));
        
        Ok(Self {
            rpc_client,
            payer,
        })
    }

    /// 🔍 扫描所有相关账户（空账户和nonce账户）
    pub async fn scan_all_accounts(&self) -> Vec<AccountType> {
        let mut accounts = Vec::new();
        
        info!("🔍 开始扫描所有相关账户...");
        
        // 检查已知的具体账户地址
        let known_addresses = vec![
            "FooFXTbMCpP3Wut4jLsSi12pexf9PsEgcoJ4iEYmxy6D", // 从交易记录中找到的
        ];
        
        for addr_str in known_addresses {
            if let Ok(pubkey) = addr_str.parse::<solana_sdk::pubkey::Pubkey>() {
                info!("🔍 检查已知账户地址: {}", pubkey);
                
                match self.rpc_client.get_account(&pubkey) {
                    Ok(account) => {
                        info!("📊 账户信息: {} SOL, {} 字节, 所有者: {}", 
                            account.lamports as f64 / 1e9, account.data.len(), account.owner);
                        
                        // 详细输出账户原始数据用于调试
                        if account.data.len() >= 16 {
                            let hex_data = account.data[..16].iter()
                                .map(|b| format!("{:02x}", b))
                                .collect::<Vec<_>>()
                                .join(" ");
                            info!("📋 账户前16字节数据: {}", hex_data);
                        }
                        
                        if account.data.len() == 80 && account.owner == solana_sdk::system_program::id() {
                            // 检查是否是全0数据（未初始化的nonce账户预留空间）
                            let is_all_zero = account.data.iter().all(|&x| x == 0);
                            
                            if is_all_zero {
                                // 未初始化的80字节nonce账户空间
                                let empty_info = EmptyAccountInfo {
                                    address: pubkey,
                                    lamports: account.lamports,
                                    data_len: account.data.len(),
                                    owner: account.owner,
                                };
                                accounts.push(AccountType::Empty(empty_info.clone()));
                                info!("💰 找到未初始化的nonce账户空间: {} | {} SOL | {} 字节 (全零数据)", 
                                    pubkey, account.lamports as f64 / 1e9, account.data.len());
                            } else {
                                // 尝试解析为已初始化的nonce账户
                                if let Ok(mut nonce_info) = self.parse_nonce_account_data(&account.data) {
                                    nonce_info.address = pubkey;
                                    nonce_info.lamports = account.lamports;
                                    accounts.push(AccountType::Nonce(nonce_info.clone()));
                                    info!("✅ 找到nonce账户: {} | {} | Authority: {} | {} SOL", 
                                        pubkey,
                                        if nonce_info.is_initialized { "✅ 已初始化" } else { "❌ 未初始化" },
                                        nonce_info.authority,
                                        account.lamports as f64 / 1e9
                                    );
                                } else {
                                    // 解析失败的80字节系统账户
                                    let empty_info = EmptyAccountInfo {
                                        address: pubkey,
                                        lamports: account.lamports,
                                        data_len: account.data.len(),
                                        owner: account.owner,
                                    };
                                    accounts.push(AccountType::Empty(empty_info.clone()));
                                    info!("💰 找到无法解析的80字节账户: {} | {} SOL", 
                                        pubkey, account.lamports as f64 / 1e9);
                                }
                            }
                        } else if account.owner == solana_sdk::system_program::id() && account.lamports > 0 {
                            // 空的系统账户，可以回收租金
                            let empty_info = EmptyAccountInfo {
                                address: pubkey,
                                lamports: account.lamports,
                                data_len: account.data.len(),
                                owner: account.owner,
                            };
                            accounts.push(AccountType::Empty(empty_info.clone()));
                            info!("💰 找到可回收空账户: {} | {} SOL | {} 字节", 
                                pubkey, account.lamports as f64 / 1e9, account.data.len());
                        }
                    }
                    Err(e) => {
                        info!("❌ 获取账户失败: {} - {}", pubkey, e);
                    }
                }
            }
        }
        
        // 生成多个确定性账户地址进行检查  
        let seeds = vec![
            "multi-tip-nonce-account",
            "nonce-account", 
            "trading-nonce",
            "backup-nonce-1",
            "backup-nonce-2",
            "backup-nonce-3",
            "main-nonce",
            "secondary-nonce",
        ];
        
        for seed in seeds {
            let account_pubkey = {
                use sha2::{Sha256, Digest};
                let mut hasher = Sha256::new();
                hasher.update(seed.as_bytes());
                hasher.update(&self.payer.pubkey().to_bytes());
                let hash = hasher.finalize();
                let mut seed_bytes = [0u8; 32];
                seed_bytes.copy_from_slice(&hash[..32]);
                
                match solana_sdk::signature::Keypair::from_seed(&seed_bytes) {
                    Ok(keypair) => keypair.pubkey(),
                    Err(_) => continue,
                }
            };
            
            info!("🔍 检查账户地址 ({}): {}", seed, account_pubkey);
            
            match self.rpc_client.get_account(&account_pubkey) {
                Ok(account) => {
                    if account.data.len() == 80 && account.owner == solana_sdk::system_program::id() {
                        // nonce账户
                        if let Ok(mut nonce_info) = self.parse_nonce_account_data(&account.data) {
                            nonce_info.address = account_pubkey;
                            nonce_info.lamports = account.lamports;
                            
                            if nonce_info.authority == self.payer.pubkey() {
                                accounts.push(AccountType::Nonce(nonce_info.clone()));
                                info!("✅ 找到有效nonce账户 ({}): {} | {} | {} SOL", 
                                    seed,
                                    account_pubkey,
                                    if nonce_info.is_initialized { "✅ 已初始化" } else { "❌ 未初始化" },
                                    account.lamports as f64 / 1e9
                                );
                            }
                        }
                    } else if account.owner == solana_sdk::system_program::id() && account.lamports > 0 {
                        // 空账户
                        let empty_info = EmptyAccountInfo {
                            address: account_pubkey,
                            lamports: account.lamports,
                            data_len: account.data.len(),
                            owner: account.owner,
                        };
                        accounts.push(AccountType::Empty(empty_info));
                        info!("💰 找到可回收空账户 ({}): {} | {} SOL", 
                            seed, account_pubkey, account.lamports as f64 / 1e9);
                    }
                }
                Err(_) => {
                    // 账户不存在
                }
            }
        }
        
        info!("📊 账户扫描完成 - 发现 {} 个账户", accounts.len());
        accounts
    }

    /// 解析nonce账户数据
    fn parse_nonce_account_data(&self, data: &[u8]) -> Result<NonceAccountInfo> {
        if data.len() != 80 {
            return Err(anyhow!("数据长度不正确，期望80字节，实际{}字节", data.len()));
        }
        
        // Solana nonce账户的实际数据结构 (根据initializeNonce指令):
        // 参考: https://docs.solana.com/developing/runtime-facilities/programs#system-program
        
        // 检查是否全为0（未初始化）
        if data.iter().all(|&x| x == 0) {
            return Err(anyhow!("账户数据全为零，未初始化"));
        }
        
        // 尝试按照实际的nonce账户结构解析
        // Nonce账户使用以下布局（可能因版本而异）:
        
        // 方法1: 检查是否有有效的公钥在常见位置
        let mut authority = Pubkey::default();
        let mut nonce_value = Hash::default();
        let mut found_valid_structure = false;
        
        // 尝试在不同位置查找authority（32字节的有效公钥）
        for offset in [8, 4, 0] {
            if offset + 32 <= data.len() {
                let potential_authority_bytes: [u8; 32] = data[offset..offset+32]
                    .try_into()
                    .unwrap();
                let potential_authority = Pubkey::new_from_array(potential_authority_bytes);
                
                // 检查是否是有效的非零公钥
                if !potential_authority_bytes.iter().all(|&x| x == 0) {
                    authority = potential_authority;
                    
                    // 查找nonce值（通常在authority附近）
                    for nonce_offset in [offset + 32, offset + 40, 40, 8] {
                        if nonce_offset + 32 <= data.len() {
                            let nonce_bytes: [u8; 32] = data[nonce_offset..nonce_offset+32]
                                .try_into()
                                .unwrap();
                            if !nonce_bytes.iter().all(|&x| x == 0) {
                                nonce_value = Hash::new_from_array(nonce_bytes);
                                found_valid_structure = true;
                                info!("🔍 找到有效结构: authority @ {}, nonce @ {}", offset, nonce_offset);
                                break;
                            }
                        }
                    }
                    if found_valid_structure {
                        break;
                    }
                }
            }
        }
        
        if !found_valid_structure {
            return Err(anyhow!("无法找到有效的nonce账户结构"));
        }
        
        // 检查authority是否是我们的公钥
        let is_initialized = authority == self.payer.pubkey();
        
        info!("🔍 解析结果: authority={}, matches_payer={}", authority, is_initialized);
        
        Ok(NonceAccountInfo {
            address: Pubkey::default(),
            nonce_value,
            is_initialized,
            authority,
            fee_calculator_lamports_per_signature: 5000, // 默认值
            lamports: 0,
        })
    }

    /// 💰 回收空账户租金  
    pub async fn reclaim_empty_account(&self, empty_account: &EmptyAccountInfo) -> Result<String> {
        info!("💰 开始回收空账户租金: {} ({} SOL)", 
            empty_account.address, empty_account.lamports as f64 / 1e9);
        
        // 对于未初始化的80字节nonce账户空间，我们可以尝试close它
        // 但需要找到对应的私钥或者用系统指令关闭
        warn!("⚠️ 无法直接回收未初始化账户的租金: {}", empty_account.address);
        warn!("   建议: 1) 初始化为nonce账户后再关闭 2) 找到对应私钥进行转账");
        
        Err(anyhow!("未初始化账户无法直接回收，需要相应私钥"))
    }
    
    /// 🗑️ 关闭nonce账户并回收租金
    pub async fn close_nonce_account(&self, nonce_account: &NonceAccountInfo) -> Result<String> {
        info!("🗑️ 开始关闭nonce账户: {} ({} SOL)", 
            nonce_account.address, nonce_account.lamports as f64 / 1e9);
        
        // 创建关闭nonce账户指令
        let withdraw_instruction = system_instruction::withdraw_nonce_account(
            &nonce_account.address,
            &nonce_account.authority,
            &self.payer.pubkey(),
            nonce_account.lamports,
        );
        
        let recent_blockhash = self.rpc_client.get_latest_blockhash()?;
        let transaction = Transaction::new_signed_with_payer(
            &[withdraw_instruction],
            Some(&self.payer.pubkey()),
            &[&*self.payer],
            recent_blockhash,
        );
        
        match self.rpc_client.send_and_confirm_transaction(&transaction) {
            Ok(signature) => {
                info!("✅ Nonce账户关闭成功: {} | {} SOL | 交易: {}", 
                    nonce_account.address,
                    nonce_account.lamports as f64 / 1e9,
                    signature
                );
                Ok(signature.to_string())
            }
            Err(e) => {
                error!("❌ Nonce账户关闭失败: {} - {}", nonce_account.address, e);
                Err(anyhow!("Nonce账户关闭失败: {}", e))
            }
        }
    }

    /// 🚀 批量回收所有账户租金
    pub async fn reclaim_all_rent(&self, accounts: Vec<AccountType>) -> Result<Vec<String>> {
        let mut signatures = Vec::new();
        
        info!("🚀 开始批量回收账户租金，共 {} 个账户", accounts.len());
        
        for account in accounts {
            match account {
                AccountType::Empty(empty_account) => {
                    match self.reclaim_empty_account(&empty_account).await {
                        Ok(signature) => signatures.push(signature),
                        Err(e) => error!("空账户回收失败: {}", e),
                    }
                }
                AccountType::Nonce(nonce_account) => {
                    match self.close_nonce_account(&nonce_account).await {
                        Ok(signature) => signatures.push(signature),
                        Err(e) => error!("Nonce账户关闭失败: {}", e),
                    }
                }
            }
            
            // 避免RPC限制，稍微延迟
            tokio::time::sleep(std::time::Duration::from_millis(500)).await;
        }
        
        info!("✅ 租金回收完成，成功处理 {} 个账户", signatures.len());
        Ok(signatures)
    }

    /// 获取nonce账户的当前nonce值
    fn get_nonce_data(&self, nonce_pubkey: &Pubkey) -> Result<Hash> {
        let account = self
            .rpc_client
            .get_account(nonce_pubkey)
            .map_err(|e| anyhow!("获取nonce账户数据失败: {}", e))?;

        // 解析nonce账户数据
        if account.data.len() < 32 {
            return Err(anyhow!("无效的nonce账户数据长度"));
        }

        // nonce值存储在账户数据的前32字节
        let nonce_bytes: [u8; 32] = account.data[0..32]
            .try_into()
            .map_err(|_| anyhow!("解析nonce数据失败"))?;

        Ok(Hash::new_from_array(nonce_bytes))
    }

    /// 🔍 查找与当前钱包相关的确定性nonce账户
    pub async fn find_deterministic_nonce_account(&self) -> Option<NonceAccountInfo> {
        // 生成确定性的nonce账户地址（与原来的逻辑一致）
        let expected_nonce_pubkey = {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            hasher.update(b"multi-tip-nonce-account");
            hasher.update(&self.payer.pubkey().to_bytes());
            let hash = hasher.finalize();
            let mut seed = [0u8; 32];
            seed.copy_from_slice(&hash[..32]);
            
            // 使用seed生成确定性的keypair
            match Keypair::from_seed(&seed) {
                Ok(keypair) => keypair.pubkey(),
                Err(_) => return None,
            }
        };
        
        info!("🔍 检查确定性nonce账户地址: {}", expected_nonce_pubkey);
        
        // 检查这个确定性地址是否存在且是有效的nonce账户
        match self.rpc_client.get_account(&expected_nonce_pubkey) {
            Ok(account) => {
                if account.data.len() == 80 && account.owner == solana_sdk::system_program::id() {
                    if let Ok(mut nonce_info) = self.parse_nonce_account_data(&account.data) {
                        nonce_info.address = expected_nonce_pubkey;
                        info!("✅ 找到确定性nonce账户: {} (已初始化: {})", 
                            expected_nonce_pubkey, 
                            nonce_info.is_initialized
                        );
                        return Some(nonce_info);
                    }
                }
            }
            Err(_) => {
                info!("❌ 确定性nonce账户不存在: {}", expected_nonce_pubkey);
            }
        }
        
        None
    }

    /// 🚀 完整的nonce账户发现流程
    pub async fn discover_nonce_accounts(&self) -> Vec<NonceAccountInfo> {
        let mut all_nonce_accounts = Vec::new();
        
        // 第1步：查找确定性nonce账户
        if let Some(deterministic_nonce) = self.find_deterministic_nonce_account().await {
            all_nonce_accounts.push(deterministic_nonce);
        }
        
        // 第2步：从扫描的所有账户中提取nonce账户
        let all_accounts = self.scan_all_accounts().await;
        for account in all_accounts {
            if let AccountType::Nonce(nonce_account) = account {
                // 避免重复添加确定性账户
                if !all_nonce_accounts.iter().any(|existing| existing.address == nonce_account.address) {
                    all_nonce_accounts.push(nonce_account);
                }
            }
        }
        
        // 输出总结信息
        if all_nonce_accounts.is_empty() {
            info!("📭 未发现任何nonce账户");
        } else {
            info!("🎯 nonce账户发现完成，共找到 {} 个账户:", all_nonce_accounts.len());
            for (i, nonce) in all_nonce_accounts.iter().enumerate() {
                info!("  [{}] {} | {} | Authority: {}", 
                    i + 1,
                    nonce.address,
                    if nonce.is_initialized { "✅ 已初始化" } else { "❌ 未初始化" },
                    nonce.authority
                );
            }
        }
        
        all_nonce_accounts
    }
}