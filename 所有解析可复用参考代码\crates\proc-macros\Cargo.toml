[package]
name = "carbon-proc-macros"
version = "0.9.0"
edition = { workspace = true }
description = "Procedural macros for Carbon"
license = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer", "proc-macro"]
categories = ["encoding"]

[lib]
proc-macro = true

[dependencies]
borsh-derive-internal = { workspace = true }
hex = { workspace = true }
proc-macro2 = { workspace = true }
quote = { workspace = true }
serde = { workspace = true }
syn = { workspace = true, features = ["full"] }
