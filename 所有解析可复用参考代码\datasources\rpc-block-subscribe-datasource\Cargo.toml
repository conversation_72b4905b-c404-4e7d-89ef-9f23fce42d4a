[package]
name = "carbon-rpc-block-subscribe-datasource"
description = "RPC Block Subscribe Datasource"
license = { workspace = true }
version = "0.9.0"
edition = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer", "block", "datasource"]
categories = ["encoding"]

[dependencies]
solana-client = { workspace = true }
solana-hash = { workspace = true }

carbon-core = { workspace = true }

async-trait = { workspace = true }
futures = { workspace = true }
log = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true }
