[package]
name = "carbon-rpc-block-crawler-datasource"
description = "RPC Block Crawler Datasource"
license = { workspace = true }
version = "0.9.0"
edition = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer", "block", "datasource"]
categories = ["encoding"]

[lib]
crate-type = ["rlib"]

[dependencies]
solana-client = { workspace = true }
solana-commitment-config = { workspace = true }
solana-hash = { workspace = true }
solana-transaction-status = { workspace = true }

carbon-core = { workspace = true }

async-stream = { workspace = true }
async-trait = { workspace = true }
futures = { workspace = true }
log = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true }
