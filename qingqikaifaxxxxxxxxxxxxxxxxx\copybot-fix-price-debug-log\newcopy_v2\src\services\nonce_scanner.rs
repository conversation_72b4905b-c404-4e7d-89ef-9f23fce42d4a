use anyhow::{anyhow, Result};
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, SeedDerivable},
    signer::Signer,
    hash::Hash,
    system_instruction,
    transaction::Transaction,
};
use solana_client::rpc_client::RpcClient;
use std::sync::Arc;
use tracing::info;

/// Nonce账户信息结构
#[derive(Debug, Clone)]
pub struct NonceAccountInfo {
    pub address: Pubkey,
    pub nonce_value: Hash,
    pub is_initialized: bool,
    pub authority: Pubkey,
    pub fee_calculator_lamports_per_signature: u64,
    pub lamports: u64,
}

/// Nonce账户扫描器
pub struct NonceScanner {
    rpc_client: Arc<RpcClient>,
    payer: Arc<Keypair>,
}

impl NonceScanner {
    /// 创建新的nonce扫描器
    pub fn new(rpc_url: &str, payer: Arc<Keypair>) -> Result<Self> {
        let rpc_client = Arc::new(RpcClient::new(rpc_url.to_string()));
        
        Ok(Self {
            rpc_client,
            payer,
        })
    }

    /// 🔍 查找确定性nonce账户
    pub async fn find_deterministic_nonce_account(&self) -> Option<NonceAccountInfo> {
        // 生成确定性的nonce账户地址
        let expected_nonce_pubkey = {
            use solana_sdk::hash::{hash, Hash};
            let seed_str = format!("multi-tip-nonce-account{}", self.payer.pubkey());
            let seed_hash = hash(seed_str.as_bytes());
            let seed_bytes = seed_hash.to_bytes();
            
            match Keypair::from_seed(&seed_bytes) {
                Ok(keypair) => keypair.pubkey(),
                Err(_) => return None,
            }
        };
        
        // 检查这个确定性地址是否存在且是有效的nonce账户
        match self.rpc_client.get_account(&expected_nonce_pubkey) {
            Ok(account) => {
                if account.data.len() == 80 && account.owner == solana_sdk::system_program::id() {
                    if let Ok(mut nonce_info) = self.parse_nonce_account_data(&account.data) {
                        nonce_info.address = expected_nonce_pubkey;
                        nonce_info.lamports = account.lamports;
                        return Some(nonce_info);
                    }
                }
            }
            Err(_) => return None,
        }
        
        None
    }

    /// 🚀 创建新的确定性nonce账户
    pub async fn create_nonce_account(&self) -> Result<(Pubkey, String)> {
        // 生成确定性的nonce账户
        let nonce_keypair = {
            use solana_sdk::hash::{hash, Hash};
            let seed_str = format!("multi-tip-nonce-account{}", self.payer.pubkey());
            let seed_hash = hash(seed_str.as_bytes());
            let seed_bytes = seed_hash.to_bytes();
            
            Keypair::from_seed(&seed_bytes).map_err(|e| anyhow!("生成keypair失败: {}", e))?
        };
        
        let nonce_address = nonce_keypair.pubkey();
        
        // 计算租金
        let rent = self.rpc_client.get_minimum_balance_for_rent_exemption(80)?;
        
        // 使用create_nonce_account函数，它会返回创建和初始化指令
        let instructions = system_instruction::create_nonce_account(
            &self.payer.pubkey(),
            &nonce_address,
            &self.payer.pubkey(),
            rent,
        );
        
        // 创建交易
        let recent_blockhash = self.rpc_client.get_latest_blockhash()?;
        let transaction = Transaction::new_signed_with_payer(
            &instructions,
            Some(&self.payer.pubkey()),
            &[&*self.payer, &nonce_keypair],
            recent_blockhash,
        );
        
        // 发送交易
        match self.rpc_client.send_and_confirm_transaction(&transaction) {
            Ok(signature) => {
                Ok((nonce_address, signature.to_string()))
            }
            Err(e) => {
                Err(anyhow!("创建失败: {}", e))
            }
        }
    }

    /// 获取nonce账户的当前值用于构建交易
    pub async fn get_current_nonce_value(&self) -> Option<Hash> {
        // 生成确定性的nonce账户地址
        let nonce_pubkey = {
            use solana_sdk::hash::{hash};
            let seed_str = format!("multi-tip-nonce-account{}", self.payer.pubkey());
            let seed_hash = hash(seed_str.as_bytes());
            let seed_bytes = seed_hash.to_bytes();
            
            match Keypair::from_seed(&seed_bytes) {
                Ok(keypair) => keypair.pubkey(),
                Err(_) => return None,
            }
        };
        
        // 获取账户数据并解析nonce值
        match self.rpc_client.get_account(&nonce_pubkey) {
            Ok(account) => {
                if account.data.len() == 80 && account.owner == solana_sdk::system_program::id() {
                    // nonce值在偏移量40处（32字节）
                    if let Ok(nonce_bytes) = account.data[40..72].try_into() {
                        Some(Hash::new_from_array(nonce_bytes))
                    } else {
                        None
                    }
                } else {
                    None
                }
            }
            Err(_) => None,
        }
    }

    /// 解析nonce账户数据（简化版）
    fn parse_nonce_account_data(&self, data: &[u8]) -> Result<NonceAccountInfo> {
        if data.len() != 80 {
            return Err(anyhow!("数据长度不正确"));
        }
        
        // 检查是否全为0（未初始化）
        if data.iter().all(|&x| x == 0) {
            return Err(anyhow!("账户未初始化"));
        }
        
        // 简化解析：假设authority在偏移量8处
        let mut authority = Pubkey::default();
        let mut nonce_value = Hash::default();
        
        if let Ok(authority_bytes) = data[8..40].try_into() {
            authority = Pubkey::new_from_array(authority_bytes);
            
            if let Ok(nonce_bytes) = data[40..72].try_into() {
                nonce_value = Hash::new_from_array(nonce_bytes);
            }
        }
        
        let is_initialized = authority == self.payer.pubkey();
        
        Ok(NonceAccountInfo {
            address: Pubkey::default(),
            nonce_value,
            is_initialized,
            authority,
            fee_calculator_lamports_per_signature: 5000,
            lamports: 0,
        })
    }
}