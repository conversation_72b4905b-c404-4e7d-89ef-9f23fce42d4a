use carbon_core::{borsh, CarbonDeserialize};

#[derive(
    CarbonDeserialize, Debug, serde::Serial<PERSON>, serde::Deserialize, PartialEq, Eq, <PERSON><PERSON>, Hash,
)]
pub struct LockedCpLiquidityState {
    pub locked_lp_amount: u64,
    pub claimed_lp_amount: u64,
    pub unclaimed_lp_amount: u64,
    pub last_lp: u64,
    pub last_k: u128,
    pub recent_epoch: u64,
    pub pool_id: solana_pubkey::Pubkey,
    pub fee_nft_mint: solana_pubkey::Pubkey,
    pub locked_owner: solana_pubkey::Pubkey,
    pub locked_lp_mint: solana_pubkey::Pubkey,
    pub padding: [u64; 8],
}
