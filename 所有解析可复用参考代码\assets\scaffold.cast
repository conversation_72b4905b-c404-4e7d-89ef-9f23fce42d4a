{"version": 2, "width": 156, "height": 43, "timestamp": 1742421852, "env": {"SHELL": "/opt/homebrew/bin/fish", "TERM": "xterm-ghostty"}}
[0.01357, "o", "Welcome to fish, the friendly interactive shell\r\nType \u001b[32mhelp\u001b(B\u001b[m for instructions on how to use fish\r\n"]
[0.014914, "o", "\u001b[?2004h"]
[0.031231, "o", "\u001b]0;~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[0.034494, "o", "\u001b]0;~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r\u001b[92mSonic\u001b(B\u001b[m@\u001b(B\u001b[mDemo\u001b(B\u001b[m \u001b[32m~/P/carbon\u001b(B\u001b[m (main)\u001b(B\u001b[m> \u001b[K\r\u001b[53C"]
[1.331543, "o", "\u001b[23D\u001b[34m\u001b[30m\u001b(B\u001b[m \u001b[36m\u001b[30m\u001b(B\u001b[m \u001b[36m\u001b[30m\u001b(B\u001b[m \u001b[36mcarbon-cli\u001b[30m\u001b(B\u001b[m\r\u001b[76C"]
[2.255868, "o", "\r\u001b[76C\r\n\u001b[30m\u001b(B\u001b[m"]
[2.256014, "o", "\u001b[?2004l"]
[2.258026, "o", "\u001b]0;carbon- ~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[2.815543, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Chose mode:  \r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14mparse\u001b[39m\r\n  scaffold\r\n\u001b[38;5;14m[\u001b[39m\u001b[38;5;14m↑↓ to move, enter to select, type to filter"]
[2.815669, "o", "\u001b[39m\u001b[38;5;14m]\u001b[39m\r\u001b[3A\u001b[14C\u001b[?25h"]
[3.181477, "o", "\u001b[?25l\u001b[14D\r\n  parse\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14mscaffold\u001b[39m\u001b[K\r"]
[3.181814, "o", "\n\r\u001b[3A\u001b[14C\u001b[?25h"]
[4.651947, "o", "\u001b[?25l\u001b[14D\u001b[38;5;10m>\u001b[39m Chose mode: \u001b[38;5;14mscaffold\u001b[39m\u001b[K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[?25h"]
[4.652582, "o", "\u001b[3A\u001b[?25h"]
[4.653009, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m project name:  \r\u001b[16C\u001b[?25h"]
[5.580112, "o", "\u001b[?25l\u001b[16D\u001b[38;5;10m?\u001b[39m project name: d \u001b[K\r\u001b[17C\u001b[?25h"]
[5.671559, "o", "\u001b[?25l\u001b[17D\u001b[38;5;10m?\u001b[39m project name: de \u001b[K\r\u001b[18C\u001b[?25h"]
[5.903949, "o", "\u001b[?25l\u001b[18D\u001b[38;5;10m?\u001b[39m project name: deg \u001b[K\r\u001b[19C\u001b[?25h"]
[5.984676, "o", "\u001b[?25l\u001b[19D\u001b[38;5;10m?\u001b[39m project name: dege \u001b[K\r\u001b[20C\u001b[?25h"]
[6.208064, "o", "\u001b[?25l\u001b[20D\u001b[38;5;10m?\u001b[39m project name: degen \u001b[K\r\u001b[21C\u001b[?25h"]
[6.590244, "o", "\u001b[?25l\u001b[21D\u001b[38;5;10m?\u001b[39m project name: degen- \u001b[K\r\u001b[22C\u001b[?25h"]
[6.883336, "o", "\u001b[?25l\u001b[22D\u001b[38;5;10m?\u001b[39m project name: degen-p \u001b[K\r\u001b[23C\u001b[?25h"]
[6.985287, "o", "\u001b[?25l\u001b[23D\u001b[38;5;10m?\u001b[39m project name: degen-pa \u001b[K\r\u001b[24C\u001b[?25h"]
[7.231049, "o", "\u001b[?25l\u001b[24D\u001b[38;5;10m?\u001b[39m project name: degen-par \u001b[K\r\u001b[25C\u001b[?25h"]
[7.306773, "o", "\u001b[?25l\u001b[25D\u001b[38;5;10m?\u001b[39m project name: degen-para \u001b[K\r\u001b[26C\u001b[?25h"]
[7.614079, "o", "\u001b[?25l\u001b[26D\u001b["]
[7.614165, "o", "38;5;10m?\u001b[39m project name: degen-parad \u001b[K\r\u001b[27C\u001b[?25h"]
[7.774423, "o", "\u001b[?25l\u001b[27D\u001b[38;5;10m?\u001b[39m project name: degen-paradi \u001b[K\r\u001b[28C\u001b[?25h"]
[7.915114, "o", "\u001b[?25l\u001b[28D\u001b[38;5;10m?\u001b[39m project name: degen-paradiz \u001b[K\r"]
[7.915448, "o", "\u001b[29C\u001b[?25h"]
[8.135764, "o", "\u001b[?25l\u001b[29D\u001b[38;5;10m?\u001b[39m project name: degen-paradize \u001b[K\r\u001b[30C\u001b[?25h"]
[8.511811, "o", "\u001b[?25l\u001b[30D\u001b[38;5;10m>\u001b[39m project name: \u001b["]
[8.511892, "o", "38;5;14mdegen-paradize\u001b[39m\u001b[K\r\n\u001b[?25h"]
[8.511918, "o", "\u001b[?25h"]
[8.512342, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Output directory:  \r\u001b[20C\u001b[?25h"]
[8.794939, "o", "\u001b[?25l\u001b[20D\u001b[38;5;10m?\u001b[39m Output directory: . \u001b[K\r\u001b[21C\u001b[?25h"]
[9.985078, "o", "\u001b[?25l\u001b[21D\u001b[38;5;10m?\u001b[39m Output directory: ./ \u001b[K\r\u001b[22C\u001b[?25h"]
[10.12306, "o", "\u001b[?25l\u001b[22D\u001b[38;5;10m?\u001b[39m Output directory: ./s \u001b[K\r\u001b[23C"]
[10.123524, "o", "\u001b[?25h"]
[10.308375, "o", "\u001b[?25l\u001b[23D\u001b[38;5;10m?\u001b[39m Output directory: ./sr \u001b[K\r\u001b[24C\u001b[?25h"]
[10.515069, "o", "\u001b[?25l\u001b[24D\u001b[38;5;10m?\u001b["]
[10.515528, "o", "39m Output directory: ./src \u001b[K\r\u001b[25C\u001b[?25h"]
[11.067356, "o", "\u001b[?25l\u001b[25D\u001b[38;5;10m>\u001b[39m Output directory: \u001b[38;5;14m./src\u001b[39m\u001b[K\r\n\u001b[?25h"]
[11.067529, "o", "\u001b[?25h"]
[11.068226, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m select a datasource:  \r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14mhelius-atlas-ws\u001b[39m\r\n  rpc-block-subscribe\r\n  rpc-program-subscribe\r\n  rpc-transaction-crawler"]
[11.068276, "o", "\r\n  yellowstone-grpc\r\n\u001b[38;5;14m[\u001b[39m\u001b[38;5;14m↑↓ to move, enter to select, type to filter\u001b[39m\u001b[38;5;14m]\u001b[39m\r\u001b[6A\u001b[23C\u001b[?25h"]
[12.134675, "o", "\u001b[?25l\u001b[23D\r\n  helius-atlas-ws\u001b[K\r\n\u001b[38;5;14m>"]
[12.134947, "o", "\u001b[39m \u001b[38;5;14mrpc-block-subscribe\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\u001b[6A\u001b[23C\u001b[?25h"]
[12.336081, "o", "\u001b[?25l\u001b[23D\r\n\r\n  rpc-block-subscribe\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14mrpc-program-subscribe\u001b[39m\u001b[K\r\n\r\n\r\n\r\u001b[6"]
[12.336316, "o", "A\u001b[23C\u001b[?25h"]
[12.550998, "o", "\u001b[?25l\u001b[23D\r\n\r\n\r\n  rpc-program-subscribe"]
[12.55109, "o", "\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14mrpc-transaction-crawler\u001b[39m\u001b[K\r\n\r\n\r\u001b[6A\u001b[23C\u001b[?25h"]
[12.73519, "o", "\u001b[?25l\u001b[23D\r\n\r\n\r\n\r\n  rpc-transaction-crawler\u001b[K"]
[12.73552, "o", "\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14myellowstone-grpc\u001b[39m\u001b[K\r\n\r\u001b[6A\u001b[23C\u001b[?25h"]
[13.199793, "o", "\u001b[?25l\u001b[23D\u001b[38;5;10m>\u001b[39m select a datasource: \u001b[38;5;14myellowstone-grpc\u001b[39m\u001b[K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r"]
[13.200127, "o", "\n\u001b[2K\r\n\u001b[?25h\u001b[6A\u001b[?25h"]
[13.200885, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m "]
[13.201123, "o", "Select the decoders for your app:  \r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mdrift-v2\u001b[39m\r\n  [ ] fluxbeam\r\n  [ ] jupiter-dca\r\n  [ ] jupiter-limit-order\r\n  [ ] jupiter-limit-order-2\r\n  [ ] jupiter-perpetuals\r\nv [ ] jupiter-swap\r\n\u001b[38;5;14m[\u001b[39m\u001b[38;5;14m↑↓ to move, space to select one, → to all, ← to none, type to filter\u001b[39m\u001b[38;5;14m]\u001b[39m\r\u001b[8A\u001b[36C\u001b[?25h"]
[14.066355, "o", "\u001b[?25l\u001b[36D\r\n  [ ] drift-v2"]
[14.066413, "o", "\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mfluxbeam\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\n\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[14.2685, "o", "\u001b[?25l\u001b[36D\r\n\r\n  [ ] fluxbeam\u001b[K\r\n\u001b[38;5;14m"]
[14.268785, "o", ">\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mjupiter-dca\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[14.459257, "o", "\u001b[?25l\u001b[36D\r\n\r\n\r\n  [ ] jupiter-dca\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mjupiter-limit-order\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\u001b[8"]
[14.459575, "o", "A\u001b[36C\u001b[?25h"]
[14.670861, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] fluxbeam\u001b[K\r\n  [ ] jupiter-dca\u001b[K\r\n  [ ] jupiter-limit-order\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b["]
[14.671119, "o", "38;5;14mjupiter-limit-order-2\u001b[39m\u001b[K\r\n  [ ] jupiter-perpetuals\u001b[K\r\n  [ ] jupiter-swap\u001b[K\r\nv [ ] kamino-lending\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[14.864427, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] jupiter-dca\u001b[K\r\n  [ ] jupiter-limit-order\u001b[K\r\n  [ ] jupiter-limit-order-2\u001b[K\r\n\u001b[38;5;14m>"]
[14.864701, "o", "\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mjupiter-perpetuals\u001b[39m\u001b[K\r\n  [ ] jupiter-swap\u001b[K\r\n  [ ] kamino-lending\u001b[K\r\nv [ ] kamino-vault\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[15.086239, "o", "\u001b[?25l\u001b[36D\r\n^"]
[15.086326, "o", " [ ] jupiter-limit-order\u001b[K\r\n  [ ] jupiter-limit-order-2\u001b[K\r\n  [ ] jupiter-perpetuals\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mjupiter-swap\u001b[39m\u001b[K\r\n  [ ] kamino-lending\u001b[K\r\n  [ ] kamino-vault\u001b[K\r\nv [ ] lifinity-amm-v2"]
[15.08658, "o", "\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[15.356504, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] jupiter-limit-order-2\u001b[K\r\n  [ ] jupiter-perpetuals\u001b[K\r\n  [ ] jupiter-swap\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m"]
[15.356823, "o", " \u001b[38;5;14mkamino-lending\u001b[39m\u001b[K\r\n  [ ] kamino-vault\u001b[K\r\n  [ ] lifinity-amm-v2\u001b[K\r\nv [ ] memo-program\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[15.766791, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] jupiter-perpetuals\u001b[K\r\n  [ ] jupiter-swap\u001b[K\r\n  [ ] kamino-lending\u001b[K\r\n\u001b[38;5;14m"]
[15.766872, "o", ">\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mkamino-vault\u001b[39m\u001b[K\r\n  [ ] lifinity-amm-v2\u001b[K\r\n  [ ] memo-program\u001b[K\r\nv [ ] meteora-dlmm\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[16.113646, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] jupiter-swap\u001b[K\r\n  [ ] kamino-lending\u001b[K\r\n  "]
[16.113731, "o", "[ ] kamino-vault\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mlifinity-amm-v2\u001b[39m\u001b[K\r\n  [ ] memo-program\u001b[K\r\n  [ ] meteora-dlmm\u001b[K\r\nv [ ] moonshot\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[16.517505, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] kamino-lending\u001b[K\r\n  [ ] "]
[16.517804, "o", "kamino-vault\u001b[K\r\n  [ ] lifinity-amm-v2\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mmemo-program\u001b[39m\u001b[K\r\n  [ ] meteora-dlmm\u001b[K\r\n  [ ] moonshot\u001b[K\r\nv [ ] mpl-core\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[17.012761, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] kamino-vault\u001b[K\r\n  [ ] lifinity-amm-v2\u001b[K\r\n  [ ] memo-program\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mmeteora-dlmm\u001b[39m\u001b[K\r\n  [ ] moonshot\u001b[K\r\n  [ ] mpl-core\u001b[K\r\nv [ ] mpl-token-metadata\u001b[K\r\n\r\u001b[8A\u001b[36"]
[17.012898, "o", "C\u001b[?25h"]
[17.840335, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] lifinity-amm-v2\u001b[K\r\n  [ ] memo-program\u001b[K\r\n  [ ] meteora-dlmm\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m"]
[17.840427, "o", "[ ]\u001b[39m \u001b[38;5;14mmoonshot\u001b[39m\u001b[K\r\n  [ ] mpl-core\u001b[K\r\n  [ ] mpl-token-metadata\u001b[K\r\nv [ ] name-service\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[18.19864, "o", "\u001b[?25l\u001b[36D\r\n\r\n\r\n"]
[18.198772, "o", "\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[x]\u001b[39m \u001b[38;5;14mmoonshot\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[18.479709, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] memo-program\u001b[K\r\n  [ ] meteora-dlmm\u001b[K\r\n  \u001b[38;5;10m[x]\u001b[39m moonshot\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14"]
[18.4798, "o", "m[ ]\u001b[39m \u001b[38;5;14mmpl-core\u001b[39m\u001b[K\r\n  [ ] mpl-token-metadata\u001b[K\r\n  [ ] name-service\u001b[K\r\nv [ ] okx-dex\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[18.716228, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] meteora-dlmm\u001b[K\r\n  \u001b[38;5;10m[x]\u001b[39m moonshot\u001b[K\r\n  [ ] mpl-core"]
[18.716669, "o", "\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mmpl-token-metadata\u001b[39m\u001b[K\r\n  [ ] name-service\u001b[K\r\n  [ ] okx-dex\u001b[K\r\nv [ ] openbook-v2\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[18.972421, "o", "\u001b[?25l\u001b[36D\r\n^ \u001b[38;5;10m[x]"]
[18.972526, "o", "\u001b[39m moonshot\u001b[K\r\n  [ ] mpl-core\u001b[K\r\n  [ ] mpl-token-metadata\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mname-service\u001b[39m\u001b[K\r\n  [ ] okx-dex\u001b[K\r\n  [ ] openbook-v2\u001b[K\r\nv "]
[18.97295, "o", "[ ] orca-whirlpool\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[19.300704, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] mpl-core\u001b[K\r"]
[19.301049, "o", "\n  [ ] mpl-token-metadata\u001b[K\r\n  [ ] name-service\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mokx-dex\u001b[39m\u001b[K\r\n  [ ] openbook-v2\u001b[K\r\n  [ ] orca-whirlpool\u001b[K\r\nv [ ] phoenix-v1\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[19.615566, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] mpl-token-metadata\u001b[K\r"]
[19.615663, "o", "\n  [ ] name-service\u001b[K\r\n  [ ] okx-dex\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mopenbook-v2\u001b[39m\u001b[K\r\n  [ ] orca-whirlpool\u001b[K\r\n  [ ] phoenix-v1\u001b[K\r\nv [ ]"]
[19.615694, "o", " pumpfun\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[19.970188, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] name-service\u001b[K\r\n  [ ] okx-dex\u001b[K\r\n  [ ] openbook-v2\u001b[K\r"]
[19.970498, "o", "\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14morca-whirlpool\u001b[39m\u001b[K\r\n  [ ] phoenix-v1\u001b[K\r\n  [ ] pumpfun\u001b[K\r\nv [ ] raydium-amm-v4\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[20.516227, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] okx-dex\u001b[K\r\n  [ ] openbook-v2\u001b[K\r\n  [ ] orca-whirlpool\u001b[K\r\n\u001b[38;5;14m>\u001b[39"]
[20.51651, "o", "m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mphoenix-v1\u001b[39m\u001b[K\r\n  [ ] pumpfun\u001b[K\r\n  [ ] raydium-amm-v4\u001b[K\r\nv [ ] raydium-clmm\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[20.800086, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] openbook-v2\u001b[K\r\n  [ ] orca-whirlpool\u001b[K\r\n  [ ] phoenix-v1\u001b[K\r\n"]
[20.800188, "o", "\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[ ]\u001b[39m \u001b[38;5;14mpumpfun\u001b[39m\u001b[K\r\n  [ ] raydium-amm-v4\u001b[K\r\n  [ ] raydium-clmm\u001b[K\r\nv [ ] raydium-cpmm\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[21.193061, "o", "\u001b[?25l\u001b[36D\r\n\r\n\r\n\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[x]\u001b[39m \u001b[38;5;14mpumpfun"]
[21.193164, "o", "\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[21.626273, "o", "\u001b[?25l\u001b[36D\r\n^ [ ] orca-whirlpool\u001b[K\r\n  [ ] phoenix-v1\u001b[K\r\n  \u001b[38;5;10m[x]\u001b[39m pumpfun\u001b[K\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;"]
[21.626559, "o", "5;14m[ ]\u001b[39m \u001b[38;5;14mraydium-amm-v4\u001b[39m\u001b[K\r\n  [ ] raydium-clmm\u001b[K\r\n  [ ] raydium-cpmm\u001b[K\r\nv [ ] raydium-liquidity-locking\u001b[K\r\n\r\u001b[8A\u001b[36C\u001b[?25h"]
[22.106812, "o", "\u001b[?25l\u001b[36D\r\n\r\n\r\n\r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14m[x]\u001b[39m \u001b[38;5;14mraydium-amm-v4\u001b[39m\u001b[K\r\n\r\n\r\n\r\n\r\u001b["]
[22.107119, "o", "8A\u001b[36C\u001b[?25h"]
[22.500834, "o", "\u001b[?25l\u001b[36D"]
[22.500969, "o", "\u001b[38;5;10m>\u001b[39m Select the decoders for your app: \u001b[38;5;14mmoonshot, pumpfun, raydium-amm-v4\u001b[39m\u001b[K\r\n\u001b[2K"]
[22.501325, "o", "\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[?25h"]
[22.501379, "o", "\u001b[8A\u001b[?25h"]
[22.501619, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Select metrics:  \r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14mlog\u001b[39m\r\n "]
[22.501666, "o", " prometheus\r\n\u001b[38;5;14m[\u001b[39m\u001b[38;5;14m↑↓ to move, enter to select, type to filter\u001b[39m\u001b[38;5;14m]\u001b[39m\r\u001b[3A\u001b[18C\u001b[?25h"]
[22.592372, "o", "\u001b[?25l\u001b[18D\u001b[38;5;10m>\u001b[39m Select metrics: \u001b[38;5;14mlog"]
[22.59246, "o", "\u001b[39m\u001b[K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[?25h"]
[22.592867, "o", "\u001b[3A\u001b[?25h"]
[22.596986, "o", "\u001b[2m⏎\u001b(B\u001b[m                                                                                                                                                           \r⏎ \r\u001b[K"]
[22.597303, "o", "\u001b[?2004h"]
[22.621461, "o", "\u001b]0;~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[22.656271, "o", "\r\u001b[92mSonic\u001b(B\u001b[m@\u001b(B\u001b[mDemo\u001b(B\u001b[m \u001b[32m~/P/carbon\u001b(B\u001b[m (main)\u001b(B\u001b[m> \u001b[J\r\u001b[53C"]
