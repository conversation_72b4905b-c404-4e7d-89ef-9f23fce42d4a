[package]
name = "carbon-test-utils"
version = "0.9.0"
edition = { workspace = true }
description = "Testing utilities for Carbon"
license = { workspace = true }
keywords = ["solana", "indexer", "testing"]
categories = ["encoding"]

[dependencies]
anyhow = { workspace = true }
base64 = { workspace = true }
bs58 = { workspace = true }
hex = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
solana-account = { workspace = true }
solana-instruction = { workspace = true, default-features = false }
solana-pubkey = { workspace = true }
solana-transaction-status = { workspace = true }

[lib]
crate-type = ["rlib"]
