[package]
name = "carbon-rpc-program-subscribe-datasource"
description = "RPC Program Subscribe Datasource"
license = { workspace = true }
version = "0.9.0"
edition = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer", "program", "datasource"]
categories = ["encoding"]

[dependencies]
solana-account = { workspace = true }
solana-client = { workspace = true }
solana-pubkey = { workspace = true }

carbon-core = { workspace = true }

async-trait = { workspace = true }
futures = { workspace = true }
log = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true }
