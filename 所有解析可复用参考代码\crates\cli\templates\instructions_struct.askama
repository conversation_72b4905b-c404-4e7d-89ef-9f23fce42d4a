{%- if instruction.requires_imports %}
use super::super::types::*;
{%- endif %}
{% raw %}
use carbon_core::{CarbonDeserialize, borsh};
{% endraw %}

#[derive(CarbonDeserialize, Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone, Hash)]
#[carbon(discriminator = "{{ instruction.discriminator }}")]
pub struct {{ instruction.struct_name }}{
    {%- for arg in instruction.args %}
    pub {{ arg.name }}: {{ arg.rust_type }},
    {%- endfor %}
}

#[derive(Debug, PartialEq, Eq, Clone, Hash, serde::Serialize, serde::Deserialize)]
pub struct {{ instruction.struct_name }}InstructionAccounts {
    {%- for account in instruction.accounts %}
    pub {{ account.name }}: solana_pubkey::Pubkey,
    {%- endfor %}
}

impl carbon_core::deserialize::ArrangeAccounts for {{ instruction.struct_name }} {
    type ArrangedAccounts = {{ instruction.struct_name }}InstructionAccounts;

    fn arrange_accounts(accounts: &[solana_instruction::AccountMeta]) -> Option<Self::ArrangedAccounts> {
        let [
            {%- for i in (0..instruction.accounts.len()) %}
            {{ instruction.accounts[i].name }},
            {%- endfor %}
            _remaining @ ..
        ] = accounts else {
            return None;
        };
       

        Some({{ instruction.struct_name }}InstructionAccounts {
            {%- for account in instruction.accounts %}
            {{ account.name }}: {{ account.name }}.pubkey,
            {%- endfor %}
        })
    }
}
