{% raw %} 
use carbon_core::account::AccountDecoder; 
use carbon_core::deserialize::CarbonDeserialize;
{% endraw %} 

use super::{{ decoder_name }}; 

{%- for account in accounts %} 
pub mod {{ account.module_name -}};
{%- endfor %} 

pub enum {{ program_struct_name }} { 
    {%- for account in accounts %} 
        {{ account.struct_name }}({{ account.module_name }}::{{ account.struct_name }}), 
    {%- endfor %} 
}


impl<'a> AccountDecoder<'a> for {{ decoder_name }} { 
    type AccountType = {{ program_struct_name }};
     fn decode_account( &self, account: &solana_account::Account, ) -> Option<carbon_core::account::DecodedAccount<Self::AccountType>> { 
        {% for account in accounts %} 
            if let Some(decoded_account) = {{ account.module_name }}::{{
	    account.struct_name }}::deserialize(account.data.as_slice()) { 
            return Some(carbon_core::account::DecodedAccount { 
                lamports: account.lamports, 
                data: {{ program_struct_name }}::{{ account.struct_name }}(decoded_account), 
                owner: account.owner, 
                executable: account.executable, 
                rent_epoch: account.rent_epoch, 
            }); 
        } 
        {% endfor %} 
    None 
    } 
}
