{% raw %}

{% endraw %}

use super::{{ decoder_name }};

{%- for instruction in instructions %}
pub mod {{ instruction.module_name }};
{%- endfor %}
{%- for event in events %}
pub mod {{ event.module_name }};
{%- endfor %}

#[derive(carbon_core::InstructionType, serde::Serialize, serde::Deserialize, PartialEq, Eq, Debug, Clone, Hash)]
pub enum {{ program_instruction_enum }} {
    {%- for instruction in instructions %}
    {{ instruction.struct_name }}({{ instruction.module_name }}::{{ instruction.struct_name }}),
    {%- endfor %}
    {%- for event in events %}
    {{ event.struct_name }}({{ event.module_name }}::{{ event.struct_name }}),
    {%- endfor %}
}

impl<'a> carbon_core::instruction::InstructionDecoder<'a> for {{ decoder_name }} {
    type InstructionType = {{ program_instruction_enum }};

    fn decode_instruction(
        &self,
        instruction: &solana_instruction::Instruction,
    ) -> Option<carbon_core::instruction::DecodedInstruction<Self::InstructionType>> {
        carbon_core::try_decode_instructions!(instruction,
            {%- for instruction in instructions %}
            {{ program_instruction_enum }}::{{ instruction.struct_name }} => {{ instruction.module_name }}::{{ instruction.struct_name }},
            {%- endfor %}
            {%- for event in events %}
            {{ program_instruction_enum }}::{{ event.struct_name }} => {{ event.module_name }}::{{ event.struct_name }},
            {%- endfor %}
        )
    }
}
