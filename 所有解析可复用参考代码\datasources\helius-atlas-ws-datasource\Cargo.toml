[package]
name = "carbon-helius-atlas-ws-datasource"
description = "Helius Atlas WebSocket Datasource"
license = { workspace = true }
version = "0.9.0"
edition = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer", "helius", "datasource"]
categories = ["encoding"]

[lib]
crate-type = ["rlib"]

[dependencies]
bincode = { workspace = true }
bs58 = { workspace = true }
helius = { workspace = true }
solana-account = { workspace = true }
solana-clock = { workspace = true }
solana-program = { workspace = true }
solana-pubkey = { workspace = true }
solana-signature = { workspace = true }
solana-transaction-context = { workspace = true }
solana-transaction-status = { workspace = true }

carbon-core = { workspace = true }

async-trait = { workspace = true }
futures = { workspace = true }
log = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-util = { workspace = true }
