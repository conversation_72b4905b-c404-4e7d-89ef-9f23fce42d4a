[package]
name = "carbon-gql-server"
version = "0.9.0"
edition = { workspace = true }
description = "GraphQL Server for Carbon"
license = { workspace = true }
keywords = ["solana", "indexer", "gql", "graphql"]
categories = ["encoding"]

[dependencies]
axum = { workspace = true }
carbon-postgres-client = { workspace = true }
juniper = { workspace = true }
juniper_axum = { workspace = true }
juniper_graphql_ws = { workspace = true }
serde = { workspace = true }
solana-pubkey = { workspace = true }
tokio = { workspace = true }

[lib]
crate-type = ["rlib"]
