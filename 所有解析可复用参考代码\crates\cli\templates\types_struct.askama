{%- if type_data.requires_imports %}
use super::*;
{%- endif %}
{% raw %}
use carbon_core::{CarbonDeserialize, borsh};
{% endraw %}

{%- match type_data.kind %}

{%- when TypeKind::Struct %}

#[derive(CarbonDeserialize, Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone, Hash)]
pub struct {{ type_data.name }} {
    {%- for field in type_data.fields %}
    {%- if let Some(attributes) = field.attributes %}
    {{ attributes }}
    {%- endif %}
    pub {{ field.name }}: {{ field.rust_type }},
    {%- endfor %}
}

{%- when TypeKind::Enum with (variants) %}

#[derive(CarbonDeserialize, Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone, Hash)]
pub enum {{ type_data.name }} {
    {%- for variant in variants %}
    {{ variant.name -}}
    {%- if let Some(fields) = variant.fields %}
        {%- match fields %}
            {%- when EnumVariantFields::Named with (field_datas) %}
                {
                    {%- for field in field_datas %}
                    {{ field.name }}: {{ field.rust_type }},
                    {%- endfor %}
                }
            {%- when EnumVariantFields::Unnamed with (unnameds) %}
                (
                    {%- for rust_type in unnameds %}
                    {{ rust_type -}},
                    {%- endfor %}
                )
        {%- endmatch %}
    {% endif %},
    {%- endfor %}
}

{% endmatch %}

