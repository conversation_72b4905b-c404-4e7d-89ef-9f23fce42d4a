use anyhow::{anyhow, Result};
use solana_sdk::{
    transaction::VersionedTransaction,
    signature::Keypair,
    instruction::Instruction,
    pubkey::Pubkey,
    hash::Hash,
    signer::Signer,
};
use std::sync::{Arc, atomic::{AtomicU64, Ordering}};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tracing::{info, error, warn};

use crate::services::senders::{
    astralane_sender::AstralaneSender,
    blockrazor_sender::BlockRazorSender,
    oslot_sender::OslotSender,
    flashblock_sender::FlashblockSender,
};

/// 高性能多tip并发发送器
/// 核心原理：同一nonce + 不同tip地址 = 区块链层面自动互斥
pub struct MultiTipSender {
    /// 全局nonce计数器，确保所有交易使用相同nonce
    nonce_counter: Arc<AtomicU64>,
    /// 各个加速器发送器
    astralane_sender: Arc<AstralaneSender>,
    blockrazor_sender: Arc<BlockRazorSender>,
    oslot_sender: Arc<OslotSender>,
    flashblock_sender: Arc<FlashblockSender>,
}

impl MultiTipSender {
    /// 创建新的多tip发送器
    pub fn new() -> Self {
        Self {
            nonce_counter: Arc::new(AtomicU64::new(1)),
            astralane_sender: Arc::new(AstralaneSender::new()),
            blockrazor_sender: Arc::new(BlockRazorSender::new()),
            oslot_sender: Arc::new(OslotSender::new()),
            flashblock_sender: Arc::new(FlashblockSender::new()),
        }
    }

    /// 并发发送多tip交易 - 核心方法
    /// 
    /// 原理：
    /// 1. 获取共享nonce
    /// 2. 构建4个交易（相同nonce + 不同tip地址）
    /// 3. 并发发送到4个加速器
    /// 4. 区块链层面自动处理nonce冲突，只有最快的成功
    pub async fn send_multi_tip_concurrent(
        &self,
        base_transaction: &VersionedTransaction,
        tip_percentage: f64,
    ) -> Result<String> {
        // 获取共享nonce - 关键：所有交易使用同一个nonce
        let shared_nonce = self.nonce_counter.fetch_add(1, Ordering::Relaxed);
        
        info!("开始多tip并发发送，共享nonce: {}", shared_nonce);
        
        // 并发构建和发送4个交易
        let handles = self.spawn_concurrent_sends(base_transaction, tip_percentage, shared_nonce).await?;
        
        // 等待任何一个成功返回
        let result = self.wait_for_first_success(handles).await?;
        
        info!("多tip发送完成，获胜交易: {}", result);
        Ok(result)
    }

    /// 并发生成4个发送任务
    async fn spawn_concurrent_sends(
        &self,
        base_transaction: &VersionedTransaction,
        tip_percentage: f64,
        shared_nonce: u64,
    ) -> Result<Vec<JoinHandle<Result<String>>>> {
        let mut handles = Vec::with_capacity(4);
        
        // Astralane任务
        let astralane_tx = self.build_tip_transaction(
            base_transaction, 
            AstralaneSender::get_random_tip_account(), 
            tip_percentage, 
            shared_nonce
        )?;
        let astralane_sender = self.astralane_sender.clone();
        handles.push(tokio::spawn(async move {
            astralane_sender.send_transaction(&astralane_tx).await
                .map_err(|e| anyhow!("Astralane发送失败: {}", e))
        }));

        // BlockRazor任务
        let blockrazor_tx = self.build_tip_transaction(
            base_transaction, 
            BlockRazorSender::get_random_tip_account(), 
            tip_percentage, 
            shared_nonce
        )?;
        let blockrazor_sender = self.blockrazor_sender.clone();
        handles.push(tokio::spawn(async move {
            blockrazor_sender.send_transaction(&blockrazor_tx).await
                .map_err(|e| anyhow!("BlockRazor发送失败: {}", e))
        }));

        // Oslot任务
        let oslot_tx = self.build_tip_transaction(
            base_transaction, 
            OslotSender::get_random_tip_account(), 
            tip_percentage, 
            shared_nonce
        )?;
        let oslot_sender = self.oslot_sender.clone();
        handles.push(tokio::spawn(async move {
            oslot_sender.send_transaction(&oslot_tx).await
                .map_err(|e| anyhow!("Oslot发送失败: {}", e))
        }));

        // Flashblock任务
        let flashblock_tx = self.build_tip_transaction(
            base_transaction, 
            FlashblockSender::get_random_tip_account(), 
            tip_percentage, 
            shared_nonce
        )?;
        let flashblock_sender = self.flashblock_sender.clone();
        handles.push(tokio::spawn(async move {
            flashblock_sender.send_transaction(&flashblock_tx).await
                .map_err(|e| anyhow!("Flashblock发送失败: {}", e))
        }));

        Ok(handles)
    }

    /// 等待第一个成功的交易
    async fn wait_for_first_success(
        &self,
        mut handles: Vec<JoinHandle<Result<String>>>,
    ) -> Result<String> {
        use tokio::select;
        
        loop {
            if handles.is_empty() {
                return Err(anyhow!("所有加速器都发送失败"));
            }

            // 使用select!等待任何一个任务完成
            let (result, index, remaining_handles) = {
                let mut remaining = Vec::new();
                let mut completed_result = None;
                let mut completed_index = 0;

                // 检查是否有任务已完成
                for (i, handle) in handles.into_iter().enumerate() {
                    if handle.is_finished() {
                        match handle.await {
                            Ok(Ok(signature)) => {
                                completed_result = Some(Ok(signature));
                                completed_index = i;
                                break;
                            }
                            Ok(Err(e)) => {
                                warn!("加速器{}发送失败: {}", i, e);
                                continue;
                            }
                            Err(e) => {
                                error!("任务{}执行错误: {}", i, e);
                                continue;
                            }
                        }
                    } else {
                        remaining.push(handle);
                    }
                }

                if let Some(result) = completed_result {
                    (result, completed_index, remaining)
                } else {
                    // 如果没有完成的，等待一个微秒再检查
                    tokio::time::sleep(tokio::time::Duration::from_micros(1)).await;
                    handles = remaining;
                    continue;
                }
            };

            match result {
                Ok(signature) => {
                    info!("加速器{}获胜！签名: {}", index, signature);
                    
                    // 取消其他任务（它们的交易会因nonce冲突自动失效）
                    for handle in remaining_handles {
                        handle.abort();
                    }
                    
                    return Ok(signature);
                }
                Err(e) => {
                    warn!("加速器{}失败: {}", index, e);
                    handles = remaining_handles;
                    continue;
                }
            }
        }
    }

    /// 基于原交易构建新的tip交易
    /// 关键：保持相同的nonce，只替换tip地址
    fn build_tip_transaction(
        &self,
        base_transaction: &VersionedTransaction,
        tip_account: &str,
        _tip_percentage: f64,
        shared_nonce: u64,
    ) -> Result<VersionedTransaction> {
        // 这里应该基于base_transaction构建新的交易
        // 保持相同的业务指令，添加不同的tip转账指令
        // 关键：使用shared_nonce确保nonce一致
        
        // 临时实现：直接返回原交易（需要根据具体的交易构建逻辑完善）
        // TODO: 实现实际的tip交易构建逻辑
        Ok(base_transaction.clone())
    }
}

/// 静态实例，避免重复创建
static MULTI_TIP_SENDER: std::sync::OnceLock<Arc<MultiTipSender>> = std::sync::OnceLock::new();

/// 获取全局多tip发送器实例
pub fn get_multi_tip_sender() -> Arc<MultiTipSender> {
    MULTI_TIP_SENDER.get_or_init(|| Arc::new(MultiTipSender::new())).clone()
}