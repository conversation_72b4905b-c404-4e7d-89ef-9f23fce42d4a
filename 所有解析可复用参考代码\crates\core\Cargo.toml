[package]
name = "carbon-core"
version = "0.9.0"
edition = { workspace = true }
description = "Core library for Carbon"
license = { workspace = true }
readme = "README.md"
repository = { workspace = true }
keywords = ["solana", "indexer"]
categories = ["encoding"]

[features]
default = ["macros"]
macros = ["carbon-macros", "carbon-proc-macros"]

[dependencies]
solana-account = { workspace = true }
solana-hash = { workspace = true }
solana-instruction = { workspace = true, default-features = false }
solana-message = { workspace = true }
solana-program = { workspace = true }
solana-pubkey = { workspace = true }
solana-signature = { workspace = true }
solana-transaction = { workspace = true }
solana-transaction-context = { workspace = true }
solana-transaction-status = { workspace = true }

async-trait = { workspace = true }
borsh = { version = "0.10.4" }
bs58 = { workspace = true }
log = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tokio-util = { workspace = true }
uuid = { workspace = true, features = ["v4"] }

# Optional macro dependencies
carbon-macros = { workspace = true, optional = true }
carbon-proc-macros = { workspace = true, optional = true }

[lib]
crate-type = ["rlib"]

[dev-dependencies]
carbon-test-utils = { workspace = true }
solana-account-decoder-client-types = { workspace = true }
