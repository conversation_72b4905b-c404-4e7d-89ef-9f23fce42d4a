name: Release Carbon CLI

on:
  push:
    tags:
      - "v*"

jobs:
  build:
    name: Build and Release
    runs-on: ${{ matrix.os }}
    permissions:
      contents: write
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            artifact_name: carbon-cli
            asset_name: carbon-cli-linux-amd64
          - os: windows-latest
            artifact_name: carbon-cli.exe
            asset_name: carbon-cli-windows-amd64.exe
          - os: macos-latest
            artifact_name: carbon-cli
            asset_name: carbon-cli-macos-amd64

    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable

      - name: Build
        run: cargo build -p carbon-cli --release

      - name: Upload binaries to release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: target/release/${{ matrix.artifact_name }}
          asset_name: ${{ matrix.asset_name }}
          tag: ${{ github.ref }}
          overwrite: true
