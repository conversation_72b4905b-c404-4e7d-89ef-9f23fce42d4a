{%- if event.requires_imports %}
use super::super::types::*;
{%- endif %}
{% raw %}
use carbon_core::{borsh, CarbonDeserialize};
{% endraw %}

#[derive(CarbonDeserialize, Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone, Hash)]
#[carbon(discriminator = "{{ event.discriminator }}")]
pub struct {{ event.struct_name }}{
    {%- for arg in event.args %}
    pub {{ arg.name }}: {{ arg.rust_type }},
    {%- endfor %}
}

