{%- if account.requires_imports %}
use super::super::types::*;
{%- endif %}
{% raw %} 
use carbon_core::{borsh, CarbonDeserialize};

#[derive(CarbonDeserialize, Debug, serde::Serialize, serde::Deserialize)] 
{% endraw %} 

#[carbon(discriminator = "{{account.discriminator }}")] 
pub struct {{ account.struct_name }} { 
    {%- for field in account.fields %} 
        {%- if let Some(attributes) = field.attributes %}
        {{ attributes }}
        {%- endif %}
        pub {{ field.name }}: {{ field.rust_type }}, 
    {%- endfor %} 
}
