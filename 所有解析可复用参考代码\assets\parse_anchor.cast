{"version": 2, "width": 215, "height": 59, "timestamp": 1741021540, "env": {"SHELL": "/opt/homebrew/bin/fish", "TERM": "xterm-ghostty"}}
[0.037862, "o", "\u001b]0;~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[1.348575, "o", "\u001b[23D\u001b[36mcarbon-cli\u001b[30m\u001b(B\u001b[m\r\u001b[75C"]
[2.046533, "o", "\r\u001b[75C\r\n\u001b[30m\u001b(B\u001b[m"]
[2.046621, "o", "\u001b[?2004l"]
[2.048652, "o", "\u001b]0;carbon- ~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[2.261414, "o", "\u001b[1m\u001b[32m    Finished\u001b[0m `dev` profile [unoptimized + debuginfo] target(s) in 0.16s\r\n"]
[2.271103, "o", "\u001b[1m\u001b[32m     Running\u001b[0m `target/debug/carbon-cli`\r\n"]
[2.607275, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m IDL source:  \r\n\u001b[38;5;14m>\u001b[39"]
[2.607312, "o", "m \u001b[38;5;14mfile\u001b[39m\r\n  program address\r\n\u001b[38;5;14m[\u001b[39m\u001b[38;5;14m↑↓ to move, enter to select, type to filter\u001b[39m\u001b[38;5;14m"]
[2.607329, "o", "]\u001b[39m\r\u001b[3A\u001b[14C\u001b[?25h"]
[3.996917, "o", "\u001b[?25l\u001b[14D\u001b[38;5;10m>\u001b[39m IDL source: \u001b[38;5;14mfile\u001b[39m\u001b[K\r\n\u001b[2K\r\n\u001b[2K\r"]
[3.997016, "o", "\n\u001b[2K\r\n\u001b[?25h"]
[3.997502, "o", "\u001b[3A\u001b[?25h"]
[3.998493, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Path to IDL file:  \r\u001b[20C\u001b[?25h"]
[4.322931, "o", "\u001b[?25l\u001b[20D\u001b[38;5;10m?\u001b[39m Path to IDL file: . \u001b[K\r\u001b[21C\u001b[?25h"]
[4.494061, "o", "\u001b[?25l\u001b[21D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./ \u001b[K\r\u001b[22C\u001b[?25h"]
[4.798116, "o", "\u001b[?25l\u001b[22D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./p \u001b[K\r\u001b[23C\u001b[?25h"]
[5.129741, "o", "\u001b[?25l\u001b[23D\u001b[38;5;10m?\u001b[39m "]
[5.130044, "o", "Path to IDL file: ./pr \u001b[K\r\u001b[24C\u001b[?25h"]
[5.245288, "o", "\u001b[?25l\u001b[24D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./pro \u001b[K\r\u001b[25C\u001b[?25h"]
[5.507073, "o", "\u001b[?25l\u001b[25D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./prog \u001b[K\r\u001b[26C\u001b[?25h"]
[5.750311, "o", "\u001b[?25l\u001b[26D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./progr \u001b[K\r\u001b[27C\u001b[?25h"]
[5.859075, "o", "\u001b[?25l\u001b[27D\u001b[38;5;10m?\u001b[39m Path to IDL file: "]
[5.859397, "o", "./progra \u001b[K\r\u001b[28C\u001b[?25h"]
[6.112721, "o", "\u001b[?25l\u001b[28D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./program \u001b[K\r\u001b[29C\u001b[?25h"]
[6.095262, "o", "\u001b[?25l\u001b[29D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./program. \u001b[K"]
[6.095372, "o", "\r\u001b[30C\u001b[?25h"]
[6.536844, "o", "\u001b[?25l\u001b[30D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./program.j \u001b[K\r\u001b[31C\u001b[?25h"]
[6.766038, "o", "\u001b[?25l\u001b[31D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./program.js"]
[6.766411, "o", " \u001b[K\r\u001b[32C\u001b[?25h"]
[6.907402, "o", "\u001b[?25l\u001b[32D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./program.jso \u001b[K\r\u001b[33C"]
[6.907759, "o", "\u001b[?25h"]
[7.176567, "o", "\u001b[?25l\u001b[33D\u001b[38;5;10m?\u001b[39m Path to IDL file: ./program.json \u001b[K\r\u001b[34C\u001b[?25h"]
[7.886305, "o", "\u001b[?25l\u001b[34D\u001b[38;5;10m>\u001b[39m Path to IDL file: \u001b[38;5;14m./program.json\u001b[39m\u001b[K\r\n\u001b[?25h"]
[7.886397, "o", "\u001b[?25h"]
[7.886794, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Standard of program:  \r\n\u001b[38;5;14m>\u001b[39m \u001b[38;5;14manchor\u001b[39m\r\n  codama\r\n\u001b[38;5;14m[\u001b[39m\u001b[38;5;14m↑↓ to move, enter to select, type to filter\u001b[39m\u001b[38;5;14m]\u001b[39m\r\u001b[3A\u001b[23C\u001b[?25h"]
[7.153085, "o", "\u001b[?25l\u001b[23D\u001b[38;5;10m>\u001b[39m Standard of program: \u001b["]
[7.15329, "o", "38;5;14manchor\u001b[39m\u001b[K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[2K\r\n\u001b[?25h"]
[7.153752, "o", "\u001b[3A\u001b[?25h"]
[7.154215, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Output directory:  \r\u001b[20C\u001b[?25h"]
[8.888296, "o", "\u001b[?25l\u001b[20D\u001b[38;5;10m?"]
[8.888588, "o", "\u001b[39m Output directory: . \u001b[K\r\u001b[21C\u001b[?25h"]
[9.103469, "o", "\u001b[?25l\u001b[21D\u001b[38;5;10m?\u001b[39m Output directory: ./ \u001b[K\r\u001b[22C\u001b[?25h"]
[9.324353, "o", "\u001b[?25l\u001b[22D\u001b[38;5;10m?\u001b[39m Output directory: ./s \u001b[K\r\u001b[23C\u001b[?25h"]
[9.528777, "o", "\u001b[?25l\u001b[23D\u001b[38;5;10m?\u001b[39m Output directory: ./sr \u001b[K\r\u001b[24C\u001b[?25h"]
[10.753198, "o", "\u001b[?25l\u001b[24D\u001b[38;5;10m?"]
[10.753433, "o", "\u001b[39m Output directory: ./src \u001b[K\r\u001b[25C\u001b[?25h"]
[11.142902, "o", "\u001b[?25l\u001b[25D\u001b[38;5;10m?\u001b[39m Output directory: ./src/ \u001b[K\r\u001b[26C\u001b[?25h"]
[12.199489, "o", "\u001b[?25l\u001b[26D\u001b[38;5;10m?\u001b[39m Output directory: ./src/d \u001b[K\r\u001b[27C\u001b[?25h"]
[12.282754, "o", "\u001b[?25l\u001b[27D\u001b[38;5;10m?\u001b[39m Output directory: ./src/de \u001b[K\r\u001b[28C\u001b[?25h"]
[12.515688, "o", "\u001b[?25l\u001b[28D\u001b[38;5;10m?\u001b[39m Output directory: ./src/dec \u001b[K\r\u001b[29C\u001b[?25h"]
[12.723271, "o", "\u001b[?25l\u001b[29D\u001b[38;5;10m?\u001b[39m Output directory: ./src/deco \u001b[K\r\u001b[30C\u001b[?25h"]
[13.071387, "o", "\u001b[?25l\u001b[30D"]
[13.071515, "o", "\u001b[38;5;10m?\u001b[39m Output directory: ./src/decod \u001b[K\r\u001b[31C\u001b[?25h"]
[13.138986, "o", "\u001b[?25l\u001b[31D\u001b[38;5;10m?\u001b[39m Output directory: "]
[13.139263, "o", "./src/decode \u001b[K\r\u001b[32C\u001b[?25h"]
[13.303886, "o", "\u001b[?25l\u001b[32D\u001b[38;5;10m?\u001b[39m Output directory: ./src/decoder \u001b[K\r\u001b[33C\u001b[?25h"]
[14.501331, "o", "\u001b[?25l\u001b[33D\u001b[38;5;10m?\u001b[39m Output directory: ./src/decoders \u001b[K\r\u001b[34C\u001b[?25h"]
[14.813429, "o", "\u001b[?25l\u001b[34D\u001b[38;5;10m>\u001b[39m Output directory: \u001b[38;5;14m./src/decoders\u001b[39m\u001b[K\r\n\u001b[?25h"]
[14.813675, "o", "\u001b[?25h"]
[14.814022, "o", "\u001b[?25l\u001b[38;5;10m?\u001b[39m Generate as crate?  \r\u001b[21C\u001b[?25h"]
[15.061395, "o", "\u001b[?25l\u001b[21D\u001b[38;5;10m?\u001b[39m Generate as crate? y \u001b[K\r\u001b[22C\u001b[?25h"]
[15.525554, "o", "\u001b[?25l\u001b[22D\u001b[38;5;10m>\u001b["]
[15.525671, "o", "39m Generate as crate? \u001b[38;5;14mYes\u001b[39m\u001b[K\r\n\u001b[?25h"]
[15.525898, "o", "\u001b[?25h"]
[15.531276, "o", "Error parsing IDL: Error(\"missing field `discriminator`\", line: 111, column: 9)\r\n"]
[16.562483, "o", "Generated ./src/decoders/program/src/types/withdraw_params.rs\r\n"]
[16.562606, "o", "Generated ./src/decoders/program/src/types/withdrawal.rs\r\n"]
[16.562897, "o", "Generated ./src/decoders/program/src/types/mod.rs\r\nGenerated ./src/decoders/program/src/accounts/dca.rs\r\n"]
[16.562965, "o", "Generated ./src/decoders/program/src/accounts/mod.rs\r\n"]
[16.563097, "o", "Generated ./src/decoders/program/src/instructions/open_dca.rs\r\n"]
[16.563364, "o", "Generated ./src/decoders/program/src/instructions/withdraw.rs\r\n"]
[16.563447, "o", "Generated ./src/decoders/program/src/instructions/deposit.rs\r\n"]
[16.563519, "o", "Generated ./src/decoders/program/src/instructions/withdraw_fees.rs\r\n"]
[16.563592, "o", "Generated ./src/decoders/program/src/instructions/initiate_flash_fill.rs\r\n"]
[16.563665, "o", "Generated ./src/decoders/program/src/instructions/fulfill_flash_fill.rs\r\n"]
[16.56374, "o", "Generated ./src/decoders/program/src/instructions/initiate_dlmm_fill.rs\r\n"]
[16.563812, "o", "Generated ./src/decoders/program/src/instructions/fulfill_dlmm_fill.rs\r\n"]
[16.563884, "o", "Generated ./src/decoders/program/src/instructions/transfer.rs\r\n"]
[16.563965, "o", "Generated ./src/decoders/program/src/instructions/end_and_close.rs\r\n"]
[16.564043, "o", "Generated ./src/decoders/program/src/instructions/collected_fee_event.rs\r\n"]
[16.56412, "o", "Generated ./src/decoders/program/src/instructions/filled_event.rs\r\n"]
[16.564249, "o", "Generated ./src/decoders/program/src/instructions/opened_event.rs\r\n"]
[16.564325, "o", "Generated ./src/decoders/program/src/instructions/closed_event.rs\r\n"]
[16.564478, "o", "Generated ./src/decoders/program/src/instructions/withdraw_event.rs\r\n"]
[16.564494, "o", "Generated ./src/decoders/program/src/instructions/deposit_event.rs\r\n"]
[16.564578, "o", "Generated ./src/decoders/program/src/instructions/mod.rs\r\n"]
[16.564667, "o", "Generated ./src/decoders/program/src/lib.rs\r\n"]
[16.564742, "o", "Generated ./src/decoders/program/Cargo.toml\r\n"]
[16.565719, "o", "\u001b[2m⏎\u001b(B\u001b[m                                                                                                                                                                                                                      \r⏎ \r\u001b[K"]
[16.565813, "o", "\u001b[?2004h"]
[16.580191, "o", "\u001b]0;~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[16.580205, "o", "\u001b[92mSonic\u001b(B\u001b[m@\u001b(B\u001b[mDemo\u001b(B\u001b[m \u001b[32m~/P/carbon\u001b(B\u001b[m (main)\u001b(B\u001b[m> \u001b[K\r\u001b[52C"]
[16.305977, "r", "107x59"]
[18.324952, "o", "\u001b]0;~/P/carbon\u0007\u001b[30m\u001b(B\u001b[m\r"]
[18.325047, "o", "\r\u001b[92mSonic\u001b(B\u001b[m@\u001b(B\u001b[mDemo\u001b(B\u001b[m \u001b[32m~/P/carbon\u001b(B\u001b[m (main)\u001b(B\u001b[m> \u001b[J\r\u001b[52C"]
[18.162409, "o", "\r\n"]
[18.162572, "o", "\u001b[30m\u001b(B\u001b[m\u001b[30m\u001b(B\u001b[m\u001b[?2004l"]
